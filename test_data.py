#!/usr/bin/env python3
"""
Quick test script to show what data was extracted
"""

import json
import os

def show_data_summary():
    """Show a summary of the extracted data"""
    
    if not os.path.exists('phml_data/phml_complete_data.json'):
        print("No data file found. Please run the scraper first.")
        return
    
    with open('phml_data/phml_complete_data.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print("="*60)
    print("PHML NIGERIA DATA EXTRACTION SUMMARY")
    print("="*60)
    
    # Company Info
    print("\n📋 COMPANY INFORMATION:")
    print(f"Mission: {data['company_info'].get('mission', 'Not found')[:100]}...")
    print(f"Vision: {data['company_info'].get('vision', 'Not found')[:100]}...")
    print(f"Goal: {data['company_info'].get('goal', 'Not found')[:100]}...")
    print(f"Values: {len(data['company_info'].get('values', []))} values extracted")
    
    # Contact Info
    print("\n📞 CONTACT INFORMATION:")
    contact = data['contact_info']
    print(f"Phones: {contact.get('phones', [])}")
    print(f"Emails: {contact.get('emails', [])}")
    print(f"Address: {contact.get('address', 'Not found')}")
    print(f"Social Media: {len(contact.get('social_media', {}))} platforms")
    
    # Board Members
    print("\n👥 BOARD MEMBERS:")
    board_members = data['board_members']
    print(f"Total: {len(board_members)} members")
    for i, member in enumerate(board_members[:5]):  # Show first 5
        print(f"  {i+1}. {member.get('name', 'Unknown')} - {member.get('position', 'Unknown')}")
    if len(board_members) > 5:
        print(f"  ... and {len(board_members) - 5} more")
    
    # Management Team
    print("\n🏢 MANAGEMENT TEAM:")
    mgmt_team = data['management_team']
    print(f"Total: {len(mgmt_team)} members")
    print("Note: Names may need manual extraction from images")
    
    # Services
    print("\n🏥 SERVICES:")
    services = data['services']
    print(f"Total: {len(services)} services")
    for i, service in enumerate(services[:3]):  # Show first 3
        title = service.get('title', 'Unknown')[:30]
        desc = service.get('description', 'No description')[:50]
        print(f"  {i+1}. {title}... - {desc}...")
    
    # Training Data
    print("\n📚 TRAINING DATA:")
    training_data = data['all_text_content']
    print(f"Total text entries: {len(training_data)}")
    
    # Pages scraped
    print("\n🌐 PAGES SCRAPED:")
    pages = data['metadata']['pages_scraped']
    for page in pages:
        print(f"  ✓ {page}")
    
    print("\n" + "="*60)
    print("DATA FILES CREATED:")
    print("="*60)
    
    data_dir = 'phml_data'
    if os.path.exists(data_dir):
        files = os.listdir(data_dir)
        for file in files:
            file_path = os.path.join(data_dir, file)
            size = os.path.getsize(file_path)
            print(f"  📄 {file} ({size:,} bytes)")
    
    print("\n✅ Data extraction completed successfully!")
    print("💡 The training text file contains all website content for model training.")
    print("📊 Use the CSV files for structured data analysis.")
    print("🔍 Check the JSON file for complete structured data.")

if __name__ == "__main__":
    show_data_summary()
