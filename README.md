# PHML Nigeria Website Scraper

A comprehensive web scraping tool to extract all information from the Police Health Maintenance Limited (PHML) Nigeria website for training data purposes.

## Features

- **Comprehensive Data Extraction**: Scrapes all pages including home, about, services, FAQs, departments, downloads, and contact pages
- **Structured Data Output**: Saves data in multiple formats (JSON, CSV, TXT)
- **Training-Ready Text**: Extracts and cleans all text content for model training
- **Rate Limiting**: Respectful scraping with built-in delays
- **Error Handling**: Robust error handling and logging
- **Multiple Output Formats**: JSON for structured data, CSV for analysis, TXT for training

## Data Extracted

### Company Information
- Mission, Vision, and Goals
- Company values and principles
- Detailed about information

### Personnel Data
- Management team members (names, positions, photos)
- Board members (names, positions, photos)

### Services
- All services offered by PHML
- Detailed service descriptions
- Service categories

### Contact Information
- Phone numbers
- Email addresses
- Physical address
- Social media links

### Additional Data
- FAQs and answers
- Department information
- Available downloads
- All text content for training

## Installation

1. Install Python 3.7 or higher
2. Install required dependencies:
```bash
pip install -r requirements.txt
```

## Usage

### Basic Usage
```bash
python phml_scraper.py
```

### Output Files

The scraper creates a `phml_data` directory with the following files:

- `phml_complete_data.json`: Complete structured data in JSON format
- `phml_training_text.txt`: All text content formatted for training
- `management_team.csv`: Management team data
- `board_members.csv`: Board members data
- `services.csv`: Services information
- `faqs.csv`: Frequently asked questions
- `training_data.csv`: All text content with page sources
- `scraping_summary.txt`: Summary report of scraped data
- `phml_scraper.log`: Detailed scraping log

### Data Structure

The main JSON file contains:
```json
{
  "company_info": {
    "mission": "...",
    "vision": "...",
    "goal": "...",
    "values": [...],
    "about_details": [...]
  },
  "management_team": [...],
  "board_members": [...],
  "services": [...],
  "faqs": [...],
  "contact_info": {...},
  "departments": [...],
  "downloads": [...],
  "all_text_content": [...],
  "metadata": {...}
}
```

## Training Data Usage

The extracted text data is perfect for:
- Language model training
- Chatbot training for healthcare/insurance domain
- Text classification models
- Information extraction models
- Question-answering systems

## Features for Training

- **Clean Text**: All text is cleaned and normalized
- **Structured Content**: Text is organized by page source
- **Comprehensive Coverage**: Includes all website content
- **Multiple Formats**: Choose the format that works best for your training pipeline

## Logging

The scraper provides detailed logging:
- Progress updates
- Error messages
- Success confirmations
- Performance metrics

Check `phml_scraper.log` for detailed information about the scraping process.

## Error Handling

The scraper includes robust error handling for:
- Network timeouts
- Missing pages
- Malformed HTML
- Rate limiting
- File I/O errors

## Customization

You can modify the scraper to:
- Add new pages to scrape
- Change output formats
- Adjust rate limiting
- Modify text cleaning rules
- Add new data extraction patterns

## Legal and Ethical Considerations

This scraper is designed for educational and research purposes. Please ensure you:
- Respect the website's robots.txt
- Use reasonable rate limiting
- Comply with terms of service
- Use the data responsibly

## Support

For issues or questions, check the log files first. The scraper provides detailed logging to help diagnose any problems.
