#!/usr/bin/env python3
"""
PHML Nigeria Website Scraper
Extracts comprehensive data from https://www.phmlnigeria.com/ for training purposes
"""

import requests
from bs4 import BeautifulSoup
import json
import csv
import time
import re
from urllib.parse import urljoin, urlparse
from typing import Dict, List, Any
import logging
from datetime import datetime
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('phml_scraper.log'),
        logging.StreamHandler()
    ]
)

class PHMLScraper:
    def __init__(self):
        self.base_url = "https://www.phmlnigeria.com"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.scraped_data = {
            'company_info': {},
            'management_team': [],
            'board_members': [],
            'services': [],
            'faqs': [],
            'contact_info': {},
            'departments': [],
            'downloads': [],
            'all_text_content': [],
            'metadata': {
                'scrape_date': datetime.now().isoformat(),
                'base_url': self.base_url,
                'pages_scraped': []
            }
        }
        
    def get_page_content(self, url: str) -> BeautifulSoup:
        """Fetch and parse a webpage"""
        try:
            logging.info(f"Fetching: {url}")
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            time.sleep(1)  # Rate limiting
            return BeautifulSoup(response.content, 'html.parser')
        except Exception as e:
            logging.error(f"Error fetching {url}: {str(e)}")
            return None
    
    def clean_text(self, text: str) -> str:
        """Clean and normalize text content"""
        if not text:
            return ""
        # Remove extra whitespace and normalize
        text = re.sub(r'\s+', ' ', text.strip())
        # Remove special characters that might interfere with training
        text = re.sub(r'[^\w\s\-.,!?():/]', '', text)
        return text
    
    def extract_company_info(self, soup: BeautifulSoup):
        """Extract company mission, vision, goals, and values"""
        logging.info("Extracting company information...")
        
        # Mission, Vision, Goal
        mission_section = soup.find(text=re.compile("Our Mission"))
        if mission_section:
            mission_parent = mission_section.find_parent()
            if mission_parent:
                mission_text = mission_parent.find_next('p') or mission_parent.find_next('div')
                if mission_text:
                    self.scraped_data['company_info']['mission'] = self.clean_text(mission_text.get_text())
        
        vision_section = soup.find(text=re.compile("Our Vision"))
        if vision_section:
            vision_parent = vision_section.find_parent()
            if vision_parent:
                vision_text = vision_parent.find_next('p') or vision_parent.find_next('div')
                if vision_text:
                    self.scraped_data['company_info']['vision'] = self.clean_text(vision_text.get_text())
        
        goal_section = soup.find(text=re.compile("Our Goal"))
        if goal_section:
            goal_parent = goal_section.find_parent()
            if goal_parent:
                goal_text = goal_parent.find_next('p') or goal_parent.find_next('div')
                if goal_text:
                    self.scraped_data['company_info']['goal'] = self.clean_text(goal_text.get_text())
        
        # Values
        values_section = soup.find(text=re.compile("Our Values"))
        if values_section:
            values = []
            # Look for numbered sections (01, 02, 03)
            for i in range(1, 10):  # Check up to 9 values
                value_num = f"0{i}" if i < 10 else str(i)
                value_element = soup.find(text=re.compile(value_num))
                if value_element:
                    value_parent = value_element.find_parent()
                    if value_parent:
                        value_title = value_parent.find_next(text=True)
                        value_desc = value_parent.find_next('p')
                        if value_title and value_desc:
                            values.append({
                                'title': self.clean_text(value_title),
                                'description': self.clean_text(value_desc.get_text())
                            })
            self.scraped_data['company_info']['values'] = values
    
    def extract_team_members(self, soup: BeautifulSoup):
        """Extract management team and board members"""
        logging.info("Extracting team members...")
        
        # Management team
        mgmt_section = soup.find(text=re.compile("MANAGEMENT TEAM"))
        if mgmt_section:
            # Find all management team member containers
            team_containers = soup.find_all('img', src=re.compile(r'/images/management/'))
            for img in team_containers:
                member_container = img.find_parent()
                if member_container:
                    name_elem = member_container.find_next(text=True)
                    position_elem = member_container.find_all(text=True)
                    if len(position_elem) > 1:
                        self.scraped_data['management_team'].append({
                            'name': self.clean_text(name_elem) if name_elem else "",
                            'position': self.clean_text(position_elem[-1]) if position_elem else "",
                            'image_url': urljoin(self.base_url, img.get('src', ''))
                        })
        
        # Board members
        board_section = soup.find(text=re.compile("BOARD MEMBERS"))
        if board_section:
            board_containers = soup.find_all('img', src=re.compile(r'/images/board/'))
            for img in board_containers:
                member_container = img.find_parent()
                if member_container:
                    text_content = member_container.get_text()
                    lines = [line.strip() for line in text_content.split('\n') if line.strip()]
                    if len(lines) >= 2:
                        self.scraped_data['board_members'].append({
                            'name': self.clean_text(lines[0]),
                            'position': self.clean_text(' '.join(lines[1:])),
                            'image_url': urljoin(self.base_url, img.get('src', ''))
                        })
    
    def extract_services(self, soup: BeautifulSoup):
        """Extract services information"""
        logging.info("Extracting services...")
        
        services_section = soup.find(text=re.compile("SERVICES"))
        if services_section:
            # Look for numbered service sections
            for i in range(1, 10):
                service_num = f"0{i}" if i < 10 else str(i)
                service_element = soup.find(text=re.compile(service_num))
                if service_element:
                    service_parent = service_element.find_parent()
                    if service_parent:
                        service_title = service_parent.find_next(text=True)
                        service_desc = service_parent.find_next('p')
                        if service_title and service_desc:
                            self.scraped_data['services'].append({
                                'title': self.clean_text(service_title),
                                'description': self.clean_text(service_desc.get_text())
                            })
    
    def extract_contact_info(self, soup: BeautifulSoup):
        """Extract contact information"""
        logging.info("Extracting contact information...")
        
        # Phone numbers
        phone_links = soup.find_all('a', href=re.compile(r'tel:'))
        phones = [link.get('href').replace('tel:', '') for link in phone_links]
        
        # Email
        email_links = soup.find_all('a', href=re.compile(r'mailto:'))
        emails = [link.get('href').replace('mailto:', '') for link in email_links]
        
        # Address
        address_text = soup.find(text=re.compile("Plot 517"))
        address = self.clean_text(address_text) if address_text else ""
        
        # Social media
        social_links = {}
        for platform in ['facebook', 'twitter', 'instagram', 'linkedin']:
            link = soup.find('a', href=re.compile(platform, re.I))
            if link:
                social_links[platform] = link.get('href')
        
        self.scraped_data['contact_info'] = {
            'phones': phones,
            'emails': emails,
            'address': address,
            'social_media': social_links
        }
    
    def extract_all_text(self, soup: BeautifulSoup, page_name: str):
        """Extract all text content for training purposes"""
        logging.info(f"Extracting all text from {page_name}...")
        
        # Remove script and style elements
        for script in soup(["script", "style"]):
            script.decompose()
        
        # Get text content
        text = soup.get_text()
        
        # Clean and split into sentences
        sentences = [self.clean_text(sentence) for sentence in text.split('.') if sentence.strip()]
        sentences = [s for s in sentences if len(s) > 10]  # Filter out very short sentences
        
        self.scraped_data['all_text_content'].extend([{
            'page': page_name,
            'content': sentence
        } for sentence in sentences])

    def scrape_additional_pages(self):
        """Scrape additional pages for comprehensive data"""
        additional_pages = [
            '/about',
            '/our-services',
            '/faqs',
            '/get-registered',
            '/departments',
            '/downloads',
            '/contact'
        ]

        for page_path in additional_pages:
            url = urljoin(self.base_url, page_path)
            soup = self.get_page_content(url)
            if soup:
                self.scraped_data['metadata']['pages_scraped'].append(url)

                # Extract page-specific content
                if 'about' in page_path:
                    self.extract_about_page(soup)
                elif 'services' in page_path:
                    self.extract_services_page(soup)
                elif 'faqs' in page_path:
                    self.extract_faqs_page(soup)
                elif 'departments' in page_path:
                    self.extract_departments_page(soup)
                elif 'downloads' in page_path:
                    self.extract_downloads_page(soup)
                elif 'contact' in page_path:
                    self.extract_contact_page(soup)

                # Always extract all text for training
                page_name = page_path.strip('/') or 'home'
                self.extract_all_text(soup, page_name)

    def extract_about_page(self, soup: BeautifulSoup):
        """Extract detailed about page information"""
        logging.info("Extracting about page...")

        # Look for additional company information
        about_content = soup.find('main') or soup.find('body')
        if about_content:
            paragraphs = about_content.find_all('p')
            about_text = []
            for p in paragraphs:
                text = self.clean_text(p.get_text())
                if len(text) > 20:  # Filter out short/empty paragraphs
                    about_text.append(text)

            if about_text:
                self.scraped_data['company_info']['about_details'] = about_text

    def extract_services_page(self, soup: BeautifulSoup):
        """Extract detailed services information"""
        logging.info("Extracting services page...")

        # Look for service cards or sections
        service_sections = soup.find_all(['div', 'section'], class_=re.compile(r'service|card'))
        if not service_sections:
            # Fallback: look for any structured content
            service_sections = soup.find_all(['div', 'section'])

        for section in service_sections:
            title_elem = section.find(['h1', 'h2', 'h3', 'h4'])
            desc_elem = section.find('p')

            if title_elem and desc_elem:
                title = self.clean_text(title_elem.get_text())
                description = self.clean_text(desc_elem.get_text())

                if len(title) > 3 and len(description) > 20:
                    # Check if this service is already captured
                    existing = any(s['title'] == title for s in self.scraped_data['services'])
                    if not existing:
                        self.scraped_data['services'].append({
                            'title': title,
                            'description': description,
                            'source': 'services_page'
                        })

    def extract_faqs_page(self, soup: BeautifulSoup):
        """Extract FAQ information"""
        logging.info("Extracting FAQs...")

        # Look for FAQ patterns
        faq_patterns = [
            soup.find_all(['div', 'section'], class_=re.compile(r'faq|question')),
            soup.find_all(['dt', 'dd']),  # Definition lists
            soup.find_all(['h3', 'h4', 'h5'])  # Headers that might be questions
        ]

        for pattern in faq_patterns:
            for elem in pattern:
                text = self.clean_text(elem.get_text())
                if '?' in text and len(text) > 10:
                    # This looks like a question
                    question = text
                    answer_elem = elem.find_next_sibling() or elem.find_next('p')
                    answer = ""
                    if answer_elem:
                        answer = self.clean_text(answer_elem.get_text())

                    if question and len(answer) > 10:
                        self.scraped_data['faqs'].append({
                            'question': question,
                            'answer': answer
                        })

    def extract_departments_page(self, soup: BeautifulSoup):
        """Extract departments information"""
        logging.info("Extracting departments...")

        dept_sections = soup.find_all(['div', 'section', 'li'])
        for section in dept_sections:
            text = self.clean_text(section.get_text())
            if len(text) > 10 and any(keyword in text.lower() for keyword in
                                    ['department', 'division', 'unit', 'office']):
                self.scraped_data['departments'].append({
                    'name': text,
                    'description': text
                })

    def extract_downloads_page(self, soup: BeautifulSoup):
        """Extract downloads/documents information"""
        logging.info("Extracting downloads...")

        # Look for download links
        download_links = soup.find_all('a', href=re.compile(r'\.(pdf|doc|docx|xls|xlsx)$', re.I))
        for link in download_links:
            self.scraped_data['downloads'].append({
                'title': self.clean_text(link.get_text()),
                'url': urljoin(self.base_url, link.get('href', '')),
                'type': link.get('href', '').split('.')[-1].lower()
            })

    def extract_contact_page(self, soup: BeautifulSoup):
        """Extract additional contact information"""
        logging.info("Extracting contact page...")

        # Look for additional contact details
        contact_sections = soup.find_all(['div', 'section'], class_=re.compile(r'contact'))
        for section in contact_sections:
            text = self.clean_text(section.get_text())
            if len(text) > 20:
                if 'contact_page_details' not in self.scraped_data['contact_info']:
                    self.scraped_data['contact_info']['contact_page_details'] = []
                self.scraped_data['contact_info']['contact_page_details'].append(text)

    def save_data(self):
        """Save scraped data in multiple formats"""
        logging.info("Saving scraped data...")

        # Create output directory
        os.makedirs('phml_data', exist_ok=True)

        # Save as JSON
        with open('phml_data/phml_complete_data.json', 'w', encoding='utf-8') as f:
            json.dump(self.scraped_data, f, indent=2, ensure_ascii=False)

        # Save training text data
        with open('phml_data/phml_training_text.txt', 'w', encoding='utf-8') as f:
            for item in self.scraped_data['all_text_content']:
                f.write(f"[{item['page']}] {item['content']}\n")

        # Save structured data as CSV
        self.save_csv_data()

        # Save summary report
        self.save_summary_report()

        logging.info("Data saved successfully!")

    def save_csv_data(self):
        """Save structured data as CSV files"""

        # Management team CSV
        if self.scraped_data['management_team']:
            with open('phml_data/management_team.csv', 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=['name', 'position', 'image_url'])
                writer.writeheader()
                writer.writerows(self.scraped_data['management_team'])

        # Board members CSV
        if self.scraped_data['board_members']:
            with open('phml_data/board_members.csv', 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=['name', 'position', 'image_url'])
                writer.writeheader()
                writer.writerows(self.scraped_data['board_members'])

        # Services CSV
        if self.scraped_data['services']:
            with open('phml_data/services.csv', 'w', newline='', encoding='utf-8') as f:
                fieldnames = ['title', 'description', 'source']
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                for service in self.scraped_data['services']:
                    row = {k: service.get(k, '') for k in fieldnames}
                    writer.writerow(row)

        # FAQs CSV
        if self.scraped_data['faqs']:
            with open('phml_data/faqs.csv', 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=['question', 'answer'])
                writer.writeheader()
                writer.writerows(self.scraped_data['faqs'])

        # Training data CSV
        if self.scraped_data['all_text_content']:
            with open('phml_data/training_data.csv', 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=['page', 'content'])
                writer.writeheader()
                writer.writerows(self.scraped_data['all_text_content'])

    def save_summary_report(self):
        """Save a summary report of scraped data"""
        with open('phml_data/scraping_summary.txt', 'w', encoding='utf-8') as f:
            f.write("PHML Nigeria Website Scraping Summary\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Scrape Date: {self.scraped_data['metadata']['scrape_date']}\n")
            f.write(f"Base URL: {self.scraped_data['metadata']['base_url']}\n")
            f.write(f"Pages Scraped: {len(self.scraped_data['metadata']['pages_scraped'])}\n\n")

            f.write("Data Collected:\n")
            f.write(f"- Management Team Members: {len(self.scraped_data['management_team'])}\n")
            f.write(f"- Board Members: {len(self.scraped_data['board_members'])}\n")
            f.write(f"- Services: {len(self.scraped_data['services'])}\n")
            f.write(f"- FAQs: {len(self.scraped_data['faqs'])}\n")
            f.write(f"- Departments: {len(self.scraped_data['departments'])}\n")
            f.write(f"- Downloads: {len(self.scraped_data['downloads'])}\n")
            f.write(f"- Training Text Entries: {len(self.scraped_data['all_text_content'])}\n\n")

            f.write("Pages Scraped:\n")
            for page in self.scraped_data['metadata']['pages_scraped']:
                f.write(f"- {page}\n")

            f.write("\nCompany Information:\n")
            if 'mission' in self.scraped_data['company_info']:
                f.write(f"Mission: {self.scraped_data['company_info']['mission'][:100]}...\n")
            if 'vision' in self.scraped_data['company_info']:
                f.write(f"Vision: {self.scraped_data['company_info']['vision'][:100]}...\n")
            if 'goal' in self.scraped_data['company_info']:
                f.write(f"Goal: {self.scraped_data['company_info']['goal'][:100]}...\n")

    def run_scraper(self):
        """Main method to run the complete scraping process"""
        logging.info("Starting PHML Nigeria website scraping...")

        try:
            # Scrape main page
            main_soup = self.get_page_content(self.base_url)
            if main_soup:
                self.scraped_data['metadata']['pages_scraped'].append(self.base_url)

                # Extract all data from main page
                self.extract_company_info(main_soup)
                self.extract_team_members(main_soup)
                self.extract_services(main_soup)
                self.extract_contact_info(main_soup)
                self.extract_all_text(main_soup, 'home')

                # Scrape additional pages
                self.scrape_additional_pages()

                # Save all data
                self.save_data()

                logging.info("Scraping completed successfully!")
                return True
            else:
                logging.error("Failed to fetch main page")
                return False

        except Exception as e:
            logging.error(f"Scraping failed: {str(e)}")
            return False

def main():
    """Main function to run the scraper"""
    scraper = PHMLScraper()
    success = scraper.run_scraper()

    if success:
        print("\n" + "="*60)
        print("PHML NIGERIA WEBSITE SCRAPING COMPLETED!")
        print("="*60)
        print("\nData has been saved in the 'phml_data' directory:")
        print("- phml_complete_data.json: Complete structured data")
        print("- phml_training_text.txt: All text for training")
        print("- *.csv files: Individual data categories")
        print("- scraping_summary.txt: Summary report")
        print("\nCheck the log file 'phml_scraper.log' for detailed information.")
    else:
        print("Scraping failed. Check the log file for details.")

if __name__ == "__main__":
    main()
